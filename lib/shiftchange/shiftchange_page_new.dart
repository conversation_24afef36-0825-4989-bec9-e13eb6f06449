// region 导入
import 'package:collection/collection.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter/material.dart';

// 项目组件
import 'package:halo_pos/shiftchange/widget/PieChartMixin.dart';
import 'package:halo_pos/shiftchange/widget/info_popup_row.dart';
import 'package:halo_pos/shiftchange/widget/login_out_dialog.dart';
import 'package:halo_pos/shiftchange/widget/proceeds_account_detial.dart';
import 'package:halo_pos/widgets/base/base_stateful_page.dart';
import 'package:halo_pos/widgets/halo_pos_label.dart';

// 工具类
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/navigator/navigate_util.dart';
import 'package:halo_utils/utils/color_util.dart';
import 'package:halo_utils/utils/math_util.dart';
import 'package:haloui/haloui.dart';
import '../../../common/tool/date_util.dart';

// 项目内部
import '../application.dart';
import '../bill/tool/decimal_display_helper.dart';
import '../common/login/login_center.dart';
import '../common/style/app_color_helper.dart';
import '../common/style/app_colors.dart';
import '../common/tool/dialog_util.dart';
import '../common/tool/sp_custom_util.dart';
import '../common/tool/sp_tool.dart';
import '../entity/system/permission_dto.dart';
import '../iconfont/icon_font.dart';
import '../login/login_page.dart';
import '../login/model/store_model.dart';
import '../offline/offline_tool.dart';
import '../print/tool/print_tool.dart';
import '../report/entity/store_statistics_dto.dart';
import '../settting/widget/checkbox.dart';

// 当前目录
import 'entity/account_balance_dto.dart';
import 'entity/shift_changes_dto.dart';
import 'entity/shift_changes_requset.dart';
import 'model/shift_changes_model.dart';
// endregion

// region 工具函数
/// 格式化金额显示
/// 处理负号、小数位数、千位分隔符等
String _formatAmount(String amount) {
  try {
    // 处理负号
    bool isNegative = amount.startsWith('-');
    String cleanAmount = amount.replaceAll('-', '');

    // 转换为decimal进行精确计算
    Decimal value = Decimal.parse(cleanAmount);

    // 获取系统配置的金额小数位数
    int decimalPlaces = SpTool.getSystemConfig().sysDigitalTotal;

    // 处理小数位数
    String formattedAmount = value.toStringAsFixed(decimalPlaces);

    // 添加千位分隔符
    List<String> parts = formattedAmount.split('.');
    String integerPart = parts[0];
    String result = '';

    // 从右向左每三位添加一个逗号
    for (int i = integerPart.length - 1, count = 0; i >= 0; i--, count++) {
      if (count > 0 && count % 3 == 0 && i != 0) {
        result = ',' + result;
      }
      result = integerPart[i] + result;
    }

    // 如果有小数部分，加回小数部分
    if (parts.length > 1) {
      result = '$result.${parts[1]}';
    }

    return isNegative ? '-$result' : result;
  } catch (e) {
    return '0';
  }
}

/// 格式化数量显示（取整数）
/// 处理负号、取整、千位分隔符等
String _formatQuantity(String quantity) {
  try {
    // 处理负号
    bool isNegative = quantity.startsWith('-');
    String cleanQuantity = quantity.replaceAll('-', '');

    // 转换为decimal并取整
    Decimal value = Decimal.parse(cleanQuantity);
    String integerPart = value.round().toString();
    String result = '';

    // 从右向左每三位添加一个逗号
    for (int i = integerPart.length - 1, count = 0; i >= 0; i--, count++) {
      if (count > 0 && count % 3 == 0 && i != 0) {
        result = ',' + result;
      }
      result = integerPart[i] + result;
    }

    return isNegative ? '-$result' : result;
  } catch (e) {
    return '0';
  }
}
// endregion

/// 交接班页面
/// 显示销售统计、单据统计、储值金额、支付方式占比等信息
class ShiftChangePageNew extends BaseStatefulPage {
  const ShiftChangePageNew({Key? key}) : super(key: key);

  @override
  BaseStatefulPageState createState() => _ShiftChangePageNewState();
}

class _ShiftChangePageNewState extends BaseStatefulPageState<ShiftChangePageNew>
    with PieChartState {
  // region 属性
  ///打印是否开启
  bool isPrint = SpTool.getPrintShiftChanges();

  ///权限
  final PermissionDto _permissionDto = SpTool.getPermission();

  ///数据模型
  ShiftChangesDto shiftChangesDto = ShiftChangesDto.fromJson(null);
  ShiftChangesDto shiftChangesLastDto = ShiftChangesDto.fromJson(null);
  List<AccountBalanceItem> accountBalanceList = [];
  List<AccountBalanceItem> accountBalanceListNotZero = [];

  // endregion

  // region 生命周期
  @override
  Future<void>? onInitState() {
    getData();
    radius = 50 * ScreenUtil().scaleHeight;
  }

  // endregion

  // region 数据处理
  /// 获取交接班数据
  getData() {
    Map parmasNew = {
      "postBeginTime": formatDateStringToUtc(
          DateUtil.formatDate(DateUtil.getDateTimeByMs(SpTool.getLoginTime()))),
      "postEndTime": formatDateStringToUtc(DateUtil.formatDate(DateTime.now())),
      "otypeId": SpTool.getStoreInfo()!.otypeId,
      "createEtypeId": LoginCenter.getLoginUser().employeeId,
      "cashierId": SpTool.getCashierInfo().id,
      "vchtypes": [2100, 2000, 2200, 4001]
    };

    ShiftChangesModel.getShiftChangeStatistics(context, parmasNew)
        .then((value) {
      shiftChangesDto = value;
      ShiftChangesModel.getPostTimeAndVchtypeList(context, parmasNew)
          .then((valueNew) {
        _resetShiftChangesDto(valueNew);
        _getPayWaysList();
      });
    });
  }

  /// 对数据做分解，计算支付方式列表
  void _getPayWaysList() {
    var newMap = groupBy(
        accountBalanceListNotZero, (AccountBalanceItem obj) => obj.atypeId);
    newMap.forEach((key, value) {
      var sum = value.map((e) => num.parse(e.total ?? "0")).fold(
          0,
          (num previousValue, element) => num.parse(
              DecimalDisplayHelper.getTotalFixed(
                  (previousValue + element).toString())));
      incomePayWays.add(IncomePayWay(
          payWayName: value[0].atypeFullname ?? "",
          income: sum.toDouble(),
          payWayId: value[0].atypeId ?? ""));
    });
  }

  /// 重构交接班dto
  /// 处理销售、退货、换货等各类数据的统计
  _resetShiftChangesDto(List<ShiftChangesNewDto> list) {
    int saleNum = 0; //门店销售单数
    int returnNum = 0; //门店退货单单数
    int exchangeNum = 0; //门店换货单单数
    int saleNumNoPay = 0; //门店销售单数-未支付
    int exchangeNumNoPay = 0; //门店换货单单数-未支付

    Decimal cashTotal = Decimal.zero; //钱箱现金收款
    Decimal payTotal = Decimal.zero; //扫码支付收款
    Decimal noPayTotal = Decimal.zero; //扫码支付收款-未支付

    Decimal cardTotal = Decimal.zero; //银行转账收款
    Decimal storedValueTotal = Decimal.zero; //会员储值收款 已包含赠金 需要测试
    Decimal otherTotal = Decimal.zero; //其他销售额

    Decimal saleTotal = Decimal.zero; //销售出库
    Decimal returnTotal = Decimal.zero; //销售退款
    Decimal exChangeTotal = Decimal.zero; //销售退款

    Decimal rechargeTotal = Decimal.zero; //储值金额
    Decimal recharge = Decimal.zero; //充值
    Decimal rechargeReturn = Decimal.zero; //储值退款

    List<AtypeModel> multiAtype = []; //其他支付方式
    accountBalanceList.clear();

    //数据解析
    for (var element in list) {
      if (element.vchtype == 2000) {
        Decimal total = Decimal.fromBigInt(BigInt.from(0));
        try {
          total = MathUtil.parse2Decimal(element.total);
        } catch (_) {}

        AccountBalanceItem accountBalanceDto = AccountBalanceItem.fromJson({
          "billNumber": element.billNumber,
          "atypeFullname": element.payFullName,
          "total": element.total,
          "atypeId": element.atypeId
        });

        if (element.payState == 0) {
          noPayTotal = total + noPayTotal;
          continue;
        }

        if (element.paywayType == 0) {
          cashTotal = total + cashTotal;
        } else if (element.paywayType == 2) {
          payTotal = total + payTotal;
        } else if (element.paywayType == 1) {
          cardTotal = total + cardTotal;
        } else if (element.paywayType == 3) {
          storedValueTotal = total + storedValueTotal;
        } else {
          otherTotal = total + otherTotal;
          shiftChangesDto.multiAtypeItem.add(accountBalanceDto);
        }
        if (element.payState == 1) {
          accountBalanceList.add(accountBalanceDto);
          saleTotal = saleTotal + total;
        }
      } else if (element.vchtype == 2200) {
        Decimal total = Decimal.fromBigInt(BigInt.from(0));
        try {
          total = MathUtil.parse2Decimal(element.total!);
        } catch (e) {}

        AccountBalanceItem accountBalanceDto = AccountBalanceItem.fromJson({
          "billNumber": element.billNumber,
          "atypeFullname": element.payFullName,
          "total": element.total ?? "0",
          "atypeId": element.atypeId
        });
        if (element.payState == 0) {
          noPayTotal = total + noPayTotal;
          continue;
        }
        if (element.paywayType == 0) {
          cashTotal = total + cashTotal;
        } else if (element.paywayType == 2) {
          payTotal = total + payTotal;
        } else if (element.paywayType == 1) {
          cardTotal = total + cardTotal;
        } else if (element.paywayType == 3) {
          storedValueTotal = total + storedValueTotal;
        } else {
          otherTotal = total + otherTotal;
          shiftChangesDto.multiAtypeItem.add(accountBalanceDto);
        }
        if (element.payState == 1) {
          accountBalanceList.add(accountBalanceDto);
          exChangeTotal = total + exChangeTotal;
        }
      } else if (element.vchtype == 2100) {
        Decimal total = Decimal.fromBigInt(BigInt.from(0));
        try {
          total = MathUtil.parse2Decimal(element.total ?? "0");
        } catch (e) {}

        AccountBalanceItem accountBalanceDto = AccountBalanceItem.fromJson({
          "billNumber": element.billNumber,
          "atypeFullname": element.payFullName,
          "total":
              Decimal.tryParse("-${element.total ?? "0"}")?.toString() ?? "0",
          "atypeId": element.atypeId ?? "0"
        });
        if (element.payState == 0) {
          continue;
        }
        if (element.paywayType == 0) {
          cashTotal = cashTotal - total;
        } else if (element.paywayType == 2) {
          payTotal = payTotal - total;
        } else if (element.paywayType == 1) {
          cardTotal = cardTotal - total;
        } else if (element.paywayType == 3) {
          storedValueTotal = storedValueTotal - total;
        } else {
          otherTotal = otherTotal - total;
          shiftChangesDto.multiAtypeItem.add(accountBalanceDto);
        }

        accountBalanceList.add(accountBalanceDto);
        returnTotal = returnTotal + total;
      } else if (element.vchtype == 4001) {
        Decimal total = MathUtil.parse2Decimal(element.total ?? "0");

        ///判断total正负号
        if (total < Decimal.zero) {
          //储值退款
          rechargeReturn += total;
        } else {
          //储值
          recharge += total;
        }
        AccountBalanceItem accountBalanceDto = AccountBalanceItem.fromJson({
          "billNumber": element.billNumber,
          "atypeFullname": element.payFullName,
          "total": total.toString() ,
          "atypeId": element.atypeId ?? "0"
        });
        if (element.paywayType == 0) {
          // cashTotal = cashTotal + total;
        } else if (element.paywayType == 2) {
          // payTotal = payTotal + total;
        } else if (element.paywayType == 1) {
          // cardTotal = cardTotal + total;
        } else if (element.paywayType == 3) {
          // storedValueTotal = storedValueTotal + total;
        } else {
          // otherTotal = otherTotal + total;
          // shiftChangesDto.multiAtypeItem.add(accountBalanceDto);
        }

        accountBalanceList.add(accountBalanceDto);
        rechargeTotal += total;
      }
    }

    final ids = list.map((e) => e.billNumber).toSet();
    list.retainWhere((x) => ids.remove(x.billNumber));
    for (var element in list) {
      if (element.vchtype == 2000 && element.atypeId != null) {
        if (element.payState == 0) {
          saleNumNoPay++;
        } else {
          saleNum++;
        }
      } else if (element.vchtype == 2100) {
        if (element.payState == 1) {
          returnNum++;
        }
      } else if (element.vchtype == 2200) {
        if (element.payState == 0) {
          exchangeNumNoPay++;
        } else {
          exchangeNum++;
        }
      }
    }

    accountBalanceListNotZero =
        accountBalanceList.where((element) => element.atypeId != null).toList();

    AtypeModel atypeModelCash = AtypeModel.fromJson(null);
    atypeModelCash.total = cashTotal.toDouble();
    AtypeModel atypeModelPay = AtypeModel.fromJson(null);
    atypeModelPay.total = payTotal.toDouble();
    AtypeModel atypeModelCard = AtypeModel.fromJson(null);
    atypeModelCard.total = cardTotal.toDouble();
    AtypeModel atypeStoredValue = AtypeModel.fromJson(null);
    atypeStoredValue.total = storedValueTotal.toDouble();
    AtypeModel otherPay = AtypeModel.fromJson(null);
    otherPay.total = otherTotal.toDouble();

    shiftChangesDto.multiAtype = multiAtype;
    shiftChangesDto.shopOrderCount = saleNum.toDouble();
    shiftChangesDto.shopOrderNoPayCount = saleNumNoPay.toDouble();
    shiftChangesDto.returnOrderCount = returnNum.toDouble();
    shiftChangesDto.exchangeOrderCount = exchangeNum.toDouble();
    shiftChangesDto.exchangeOrderNoPayCount = exchangeNumNoPay.toDouble();
    shiftChangesDto.cashierAtype = atypeModelCash;
    shiftChangesDto.payAtype = atypeModelPay;
    shiftChangesDto.cardAtype = atypeModelCard;
    shiftChangesDto.storedValueAtype = atypeStoredValue;
    shiftChangesDto.otherAtype = otherPay;
    shiftChangesDto.saleTotal = saleTotal.toDouble();
    shiftChangesDto.returnTotal = returnTotal.toDouble();
    shiftChangesDto.exChangTotal = exChangeTotal.toDouble();
    shiftChangesDto.noPayAmount = noPayTotal.toDouble();
    shiftChangesDto.rechargeTotal = rechargeTotal.toDouble();
    shiftChangesDto.recharge = recharge.toDouble();
    shiftChangesDto.rechargeReturn = rechargeReturn.toDouble();
    setState(() {});
  }

  // endregion

  // region UI构建
  @override
  Widget buildLeftBody(BuildContext context) {
    return HaloContainer(
      direction: Axis.vertical,
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.max,
      children: [_buildCenterBody(), _buildFooterCard()],
    );
  }

  /// 构建顶部信息栏
  @override
  Widget buildTopBody(BuildContext context) {
    return HaloContainer(
      color: Colors.white,
      height: 83.h,
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        ///图片
        Container(
          margin: EdgeInsets.only(left: 24.w),
          width: 50.w,
          height: 50.w,
          child: IconFont(
            IconNames.shouyingyuan,
            size: 50.w,
          ),
        ),
        _buildTitle(spans: [
          TextSpan(
              text: "交班人: ${LoginCenter.getLoginUser().user ?? ""}",
              style: TextStyle(fontSize: 26.sp, fontWeight: FontWeight.bold))
        ], padding: EdgeInsets.only(left: 10.w), flex: 2),
        _buildTitle(spans: [
          TextSpan(
              text: "门店: ",
              style: TextStyle(
                  fontSize: 24.sp, color: ColorUtil.stringColor("#666666"))),
          TextSpan(
              text: SpTool.getStoreInfo()!.fullname ?? "",
              style: TextStyle(
                  fontSize: 26.sp, color: ColorUtil.stringColor("#333333")))
        ], padding: EdgeInsets.only(left: 76.w), flex: 3),
        _buildTitle(spans: [
          TextSpan(
              text: "收银机: ",
              style: TextStyle(
                  fontSize: 24.sp, color: ColorUtil.stringColor("#666666"))),
          TextSpan(
              text: SpTool.getCashierInfo().fullname ?? "",
              style: TextStyle(
                  fontSize: 26.sp, color: ColorUtil.stringColor("#333333")))
        ], padding: EdgeInsets.only(left: 76.w), flex: 2),
        _buildTitle(spans: [
          TextSpan(
              text: "登录时间: ",
              style: TextStyle(
                  fontSize: 24.sp, color: ColorUtil.stringColor("#666666"))),
          TextSpan(
              text: formatDateStringToLocal(
                  DateUtil.formatDateMs(SpTool.getLoginTime())),
              style: TextStyle(
                  fontSize: 26.sp,
                  color: ColorUtil.stringColor("#333333"),
                  fontWeight: FontWeight.w500))
        ], padding: EdgeInsets.only(left: 76.w), flex: 3),
      ],
    );
  }

  /// 顶部标题组件
  Widget _buildTitle(
      {required List<InlineSpan> spans, EdgeInsets? padding, int flex = 1}) {
    return Flexible(
      flex: flex,
      child: Padding(
          padding: padding ?? EdgeInsets.zero,
          child: Text.rich(TextSpan(
            children: spans,
          ))),
    );
  }

  /// 构建中间内容区域
  Widget _buildCenterBody() {
    return Expanded(
      child: HaloContainer(
        margin: EdgeInsets.all(26.w),
        padding: EdgeInsets.only(left: 10.w, right: 10.w),
        direction: Axis.vertical,
        mainAxisSize: MainAxisSize.max,
        children: [
          _buildFirstCard(),
          SizedBox(
            height: 26.w,
          ),
          _buildSecondCard()
        ],
      ),
    );
  }

  /// 构建第一行交接班卡片
  _buildFirstCard() {
    return Expanded(
        flex: 3,
        child: HaloContainer(children: [
          _buildSaleCard(flex: 5),
          _buildSaleOrderCountCard(flex: 4),
          _buildStoredValueCard(flex: 3)
        ]));
  }

  /// 构建第二行交接班卡片
  _buildSecondCard() {
    return Expanded(
        flex: 5,
        child: HaloContainer(children: [
          _buildPieChartView(flex: 14),
          _buildCashBoxCard(flex: 5)
        ]));
  }

  /// 构建销售收入卡片
  _buildSaleCard({int flex = 1}) {
    return Expanded(
      flex: flex,
      child: HaloContainer(
        color: Colors.white,
        direction: Axis.vertical,
        mainAxisSize: MainAxisSize.max,
        borderRadius: BorderRadius.all(Radius.circular(10.w)),
        children: [
          Expanded(
            child: HaloContainer(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Container(
                    padding: EdgeInsets.only(left: 20.w),
                    child: buildCardItem(
                      "销售收入(元)",
                      shiftChangesDto.saleAmount.toString(),
                      tips: "销售收入=销售出库-销售退货+销售换货",
                      subFontSize: 36.sp,
                      color: ColorUtil.stringColor("#333333"),
                    ),
                  ),
                ),
                Container(
                  alignment: Alignment.topCenter,
                  padding: EdgeInsets.only(top: 14.w, right: 23.w),
                  child: GestureDetector(
                    onTap: () {
                      _showInventory(context);
                    },
                    child: Text("明细",
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 26.sp,
                          color: ColorUtil.stringColor("#2288FC"),
                        )),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: HaloContainer(
              padding: EdgeInsets.only(
                  left: 20.w, right: 25.w, top: 10.w, bottom: 10.w),
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.max,
              children: [
                Expanded(
                  child: buildCardItem(
                    "销售出库",
                    shiftChangesDto.saleTotal.toString(),
                  ),
                ),
                Expanded(
                  child: buildCardItem(
                    "销售退款",
                    shiftChangesDto.returnTotal.toString(),
                  ),
                ),
                Expanded(
                  child: buildCardItem(
                    "销售换货",
                    shiftChangesDto.exChangTotal.toString(),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建总单据数卡片
  _buildSaleOrderCountCard({int flex = 1}) {
    return Expanded(
      flex: flex,
      child: HaloContainer(
        color: Colors.white,
        direction: Axis.vertical,
        margin: EdgeInsets.symmetric(horizontal: 25.w),
        borderRadius: BorderRadius.all(Radius.circular(10.w)),
        children: [
          Expanded(
            child: Container(
              padding: EdgeInsets.only(left: 20.w),
              child: buildCardItem(
                  "总单据数(单)",
                  (shiftChangesDto.returnOrderCount!.toInt() +
                          shiftChangesDto.shopOrderCount!.toInt() +
                          shiftChangesDto.exchangeOrderCount!.toInt())
                      .toString(),
                  tips: "总单据数=销售+退货+换货",
                  subFontSize: 36.sp,
                  isQuantity: true,
                  color: ColorUtil.stringColor("#333333")),
            ),
          ),
          Expanded(
            child: HaloContainer(
              padding: EdgeInsets.only(
                  left: 20.w, right: 25.w, top: 10.w, bottom: 10.w),
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                    child: buildCardItem(
                  "销售",
                  shiftChangesDto.shopOrderCount.toString(),
                  isQuantity: true,
                )),
                Expanded(
                    child: buildCardItem(
                  "换货",
                  shiftChangesDto.exchangeOrderCount.toString(),
                  isQuantity: true,
                )),
                Expanded(
                    child: buildCardItem(
                  "退货",
                  shiftChangesDto.returnOrderCount.toString(),
                  isQuantity: true,
                )),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建储值金额卡片
  _buildStoredValueCard({int flex = 1}) {
    return Expanded(
      flex: flex,
      child: HaloContainer(
        color: Colors.white,
        direction: Axis.vertical,
        borderRadius: BorderRadius.all(Radius.circular(10.w)),
        children: [
          Expanded(
            child: Container(
              padding: EdgeInsets.only(left: 20.w),
              child: buildCardItem(
                "储值金额(元)",
                shiftChangesDto.rechargeTotal.toString(),
                subFontSize: 36.sp,
                color: ColorUtil.stringColor("#333333"),
              ),
            ),
          ),
          Expanded(
            child: HaloContainer(
              padding: EdgeInsets.only(
                  left: 20.w, right: 25.w, top: 10.w, bottom: 10.w),
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: buildCardItem(
                    "充值",
                    shiftChangesDto.recharge.toString(),
                  ),
                ),
                Expanded(
                  child: buildCardItem(
                    "退款",
                    shiftChangesDto.rechargeReturn.toString(),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建钱箱余额卡片
  _buildCashBoxCard({int flex = 1}) {
    return Expanded(
      flex: flex,
      child: HaloContainer(
        color: Colors.white,
        direction: Axis.vertical,
        margin: EdgeInsets.only(left: 25.w),
        borderRadius: BorderRadius.all(Radius.circular(10.w)),
        children: [
          Expanded(
            child: Container(
              padding: EdgeInsets.only(left: 20.w),
              child: buildCardItem(
                "钱箱余额(元）",
                shiftChangesDto.cashBoxAmount.toString(),
                subFontSize: 36.sp,
                color: ColorUtil.stringColor("#333333"),
              ),
            ),
          ),
          Expanded(
            child: HaloContainer(
              padding: EdgeInsets.only(
                  left: 20.w, right: 25.w, top: 10.w, bottom: 10.w),
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: buildCardItem(
                    "收款金额",
                    shiftChangesDto.recAmount.toString(),
                  ),
                ),
                Expanded(
                  child: buildCardItem(
                    "退款金额",
                    shiftChangesDto.returnBoxAmount.toString(),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: HaloContainer(
              padding: EdgeInsets.only(
                  left: 20.w, right: 25.w, top: 10.w, bottom: 10.w),
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: buildCardItem(
                    "存入金额",
                    shiftChangesDto.depositAmount.toString(),
                  ),
                ),
                Expanded(
                  child: buildCardItem(
                    "取出金额",
                    shiftChangesDto.takeoutAmount.toString(),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建交接班卡片组件
  /// 支持显示标题、数值、提示信息等
  buildCardItem(String title, String subTitle,
      {Color? color,
      double? subFontSize,
      String? tips,
      bool isQuantity = false}) {
    // 根据类型选择格式化方法
    String displayValue =
        isQuantity ? _formatQuantity(subTitle) : _formatAmount(subTitle);

    return HaloContainer(
      padding: EdgeInsets.all(10.w),
      direction: Axis.vertical,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: tips != null && tips.isNotEmpty
              ? InfoPopupRow(
                  title: title,
                  tips: tips,
                  textStyle: TextStyle(
                    fontSize: 24.sp,
                    color: color ?? ColorUtil.stringColor("#666666"),
                    fontWeight: FontWeight.w500,
                  ),
                )
              : Row(
                  children: [
                    Flexible(
                      child: HaloPosLabel(
                        title,
                        textStyle: TextStyle(
                          fontSize: 24.sp,
                          color: color ?? ColorUtil.stringColor("#666666"),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
        ),
        Expanded(
          child: HaloPosLabel(
            displayValue,
            textStyle: TextStyle(
              fontSize: subFontSize ?? 32.sp,
              color: ColorUtil.stringColor("#333333"),
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  /// 构建饼图视图
  _buildPieChartView({int flex = 1}) {
    return Expanded(
      flex: flex,
      child: HaloContainer(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.start,
        color: Colors.white,
        direction: Axis.vertical,
        borderRadius: BorderRadius.all(Radius.circular(10.w)),
        children: [
          HaloContainer(
            padding: EdgeInsets.only(left: 30.w, top: 22.w),
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              HaloPosLabel("支付方式占比",
                  textStyle: TextStyle(
                      fontSize: 24.sp,
                      color: Colors.black,
                      fontWeight: FontWeight.bold)),
            ],
          ),
          Expanded(child: buildPieChart(context)),
          Padding(
            padding: EdgeInsets.only(
                left: 200 * ScreenUtil().scaleWidth, bottom: 25.w),
            child: HaloPosLabel(
              "合计收入=销售收入+储值金额",
              textStyle: TextStyle(fontSize: 25.sp, color: Colors.grey),
            ),
          )
        ],
      ),
    );
  }

  /// 构建底部工具栏
  Widget _buildFooterCard() {
    return HaloContainer(
      mainAxisAlignment: MainAxisAlignment.center,
      width: double.infinity,
      color: Colors.white,
      height: 120.w,
      children: [
        Expanded(child: cupertinoItem()),
        Padding(
          padding: EdgeInsets.only(right: 22.w),
          child: HaloButton(
            height: 80.w,
            borderRadius: 8.w,
            backgroundColor: ColorUtil.stringColor("#4679FC"),
            width: 376.w,
            fontWeight: FontWeight.w500,
            fontSize: 28.sp,
            buttonType: HaloButtonType.elevatedButton,
            onPressed: () {
              if (OffLineTool().isOfflineLogin) {
                _offLineLoginOut();
              } else {
                _onLineLoginOut();
              }
            },
            elevation: MaterialStateProperty.all(0),
            text: "交班并登出",
          ),
        ),
      ],
    );
  }

  /// 构建iOS风格的选项项
  Widget cupertinoItem() {
    return HaloContainer(
      mainAxisAlignment: MainAxisAlignment.end,
      padding: EdgeInsets.only(right: 22.w),
      children: [
        Padding(
          padding: EdgeInsets.all(10.w),
          child: _haloPosCheckBox(),
        ),
        HaloPosLabel(
          "交接班时打印小票",
          textStyle: TextStyle(
              fontSize: 28.sp, color: ColorUtil.stringColor("#333333")),
        ),
      ],
    );
  }

  /// 构建复选框
  HaloPosCheckBox _haloPosCheckBox() {
    return HaloPosCheckBox(
        value: isPrint,
        width: 32.w,
        height: 32.w,
        checkImage: IconNames.xuanzhong,
        uncheckImage: IconNames.weixuanzhong,
        onChanged: (value) {
          if (!_permissionDto.shopsaleshiftchangesprint!) {
            HaloToast.showError(context, msg: "没有更改交接班打印的权限");
            return;
          }
          setState(() {
            isPrint = value;
            SpTool.savePrintShiftChanges(value);
          });
        });
  }

  // endregion

  // region 事件处理
  /// 离线登出处理
  _offLineLoginOut() {
    HaloToast.showMsg(context, msg: "离线模式下，不支持交接班");
  }

  /// 在线登出处理
  _onLineLoginOut() {
    if (!_permissionDto.shopsaleshiftchangesout!) {
      HaloToast.showError(context, msg: "没有交接班退出的权限");
      return;
    }

    ShiftChangesModel.saveShiftChangesRecord(context, getRequsetBody())
        .then((value) async {
      shiftChangesDto.changeTime = value!.changeTime;
      if (isPrint) {
        shiftChangesDto.accountBalanceList = accountBalanceListNotZero;
        PrintTool.printShiftChanges(context, shiftChangesDto: shiftChangesDto);
      }
      await StoreModel.updateLoginRecord(context);
      PrintTool.openCashBox(context);
      if (context.mounted) {
        DialogUtil.showAlertDialog(context,
                child: LoginOutDialog(
                  isEnabled: _permissionDto.shopsaleshiftchangesprint!,
                  printClick: () {
                    ///打印
                    shiftChangesDto.accountBalanceList = accountBalanceListNotZero;
                    PrintTool.printShiftChanges(context,
                        shiftChangesDto: shiftChangesDto);
                  },
                ),
                dismissOnTouchOutside: false)
            .then((value) {
          if (value) {
            LoginCenter.loginOut(context, loginOutSuccess: () {})
                .whenComplete(() {
              SpTool.saveShiftChangesInfo(0);
              SpTool.clearPromotionList();
              SpTool.cleanTenderManageConfig();
              SpTool.cleanTransferConfig();
              SpTool.saveAutoLogin(false);
              SpTool.saveEtypeInfo("", "");
              SpCustomUtil.backupPreferences();
              _loginOut();
            });
          }
        });
      }
    });
  }

  /// 登出操作
  _loginOut() {
    debugPrint("_loginOut_begin");
    debugPrint("_loginOut_ing+++++${Application.loginOut}");
    if (null != Application.loginOut) {
      debugPrint("_loginOut_ing++++111+${Application.loginOut}");
      Application.loginOut!(context);
      return;
    }
    debugPrint("_loginOut_end");
    Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => const LoginPage()),
        (route) => false);
  }

  /// 显示收款账户明细
  _showInventory(BuildContext context) {
    if (!_permissionDto.shopsaleshiftchangessaledetail!) {
      HaloToast.showError(context, msg: "没有查看明细的权限");
      return;
    }
    DialogUtil.showAlertDialog(context,
        child: ProceedsAccountDetial(
          list: accountBalanceListNotZero,
        ));
  }

  /// 构建交接班记录请求体
  ShiftChangesRequset getRequsetBody() {
    return ShiftChangesRequset.fromJson({
      "otypeId": SpTool.getStoreInfo()!.otypeId,
      "cashierId": SpTool.getCashierInfo().id,
      "startTime":
          DateUtil.formatDate(DateUtil.getDateTimeByMs(SpTool.getLoginTime())),
      "changeTime": DateUtil.formatDate(DateTime.now()),
      "totalAmount": shiftChangesDto.saleAmount,
      "cashAmount": shiftChangesDto.recAmount ?? 0,
      "transferAmount": shiftChangesDto.cardAtype.total ?? 0,
      "storedAmount": shiftChangesDto.storedValueAtype.total ?? 0,
      "payAmount": shiftChangesDto.payAtype.total ?? 0,
      "cashboxDeposit": shiftChangesDto.depositAmount ?? 0,
      "cashboxTakeout": shiftChangesDto.takeoutAmount ?? 0,
      "returnAmount": shiftChangesDto.returnTotal?.abs() ?? 0,
      "changeAmount": shiftChangesDto.exChangTotal ?? 0,
      "saleTotal": shiftChangesDto.shopOrderCount?.toInt() ?? 0,
      "returnTotal": shiftChangesDto.returnOrderCount?.abs() ?? 0,
      "changeTotal": shiftChangesDto.exchangeOrderCount?.abs() ?? 0,
      "unPayTotal": (shiftChangesDto.shopOrderNoPayCount?.toInt() ?? 0) +
          (shiftChangesDto.exchangeOrderNoPayCount?.toInt() ?? 0),
      "unPayAmount": shiftChangesDto.noPayAmount ?? 0,
      "otherThridAmount": shiftChangesDto.otherAtype.total ?? 0,
      "saleAmount": shiftChangesDto.saleTotal ?? 0,
      "rechargeAmount": shiftChangesDto.rechargeTotal ?? 0,
      "cashboxBalance": shiftChangesDto.cashBoxAmount
    });
  }

  // endregion

  // region 标题栏
  @override
  String getActionBarTitle() {
    return "交接班";
  }

  @override
  buildAppBar() {
    return PreferredSize(
        preferredSize: Size.fromHeight(80.h),
        child: AppBar(
          backgroundColor: AppColorHelper(context).getAppBarColor(),
          titleSpacing: 0.0,
          automaticallyImplyLeading: false,
          toolbarHeight: 80.h,
          title: SizedBox(
            height: 80.h,
            child: Row(
              children: [
                GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () => NavigateUtil.pop(context),
                  child: Container(
                      padding: EdgeInsets.zero,
                      width: 80.w,
                      height: double.infinity,
                      alignment: Alignment.center,
                      child: IconFont(
                        IconNames.ngp_left_back,
                        size: 32.w,
                        color: ColorUtil.color2String(
                            AppColorHelper(context).getTitleBoldTextColor()),
                      )),
                ),
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.only(right: 80.w),
                    child: Center(
                      child: Text(
                        getActionBarTitle() ?? "",
                        style: TextStyle(
                            color: AppColors.normalTextColor,
                            fontSize: widget.titleTextSize.sp),
                      ),
                    ),
                  ),
                )
              ],
            ),
          ),
          centerTitle: true,
          actions: appBarActions(context),
        ));
  }
// endregion
}
